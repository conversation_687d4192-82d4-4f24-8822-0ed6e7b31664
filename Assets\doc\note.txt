This document outlines a comprehensive server-side development plan for a 2D virtual social game, utilizing Node.js, the Colyseus framework, TypeScript, and a MySQL database. It covers environmental setup to core module development, including account authentication, social interaction, character management, private spaces, and a fashion system.

## 1. Project Overview

The project aims to develop the server-side for a 2D virtual social game, offering core functionalities such as account authentication, social interaction, character management, private spaces, a fashion system, and a task system.

## 2. Development Environment and Dependencies

### 2.1 Colyseus Environment Installation

Install the Colyseus framework using `npm create colyseus-app@latest VirtualWrld-colyseus-server`, selecting TypeScript as the development language during installation.

### 2.2 Dependency Component Installation

* `mysql2`: For interaction with MySQL databases.
* `jsonwebtoken`: For JWT (JSON Web Token) verification.
* `uuid`: For generating unique identifiers (e.g., for furniture instances).
* `dotenv`: For loading environment variables.
* `@types/mysql2`, `@types/jsonwebtoken`, `@types/uuid`, `@types/cors`, `@types/express`, `@types/node`: TypeScript type definition files for respective libraries.

## 3. Project Structure

The project code will be organized in the `src` folder with the following subfolders:

* `auth`: Handles authentication logic.
* `rooms`: Defines Colyseus rooms.
* `models`: Defines data models and Schemas for Colyseus state synchronization.
* `services`: Contains business logic services interacting with the database.
* `utils`: Stores common utility functions like database connection.

## 4. Core Module Development

### 4.1 Server-Side Token Validation and Basic User Management

This section details a robust token authentication mechanism for the Colyseus game server, using Node.js, Colyseus, TypeScript, and MySQL. The server validates client-sent User IDs (UID) and hashed tokens against a MySQL database. It also includes a basic `users` table definition for reference.

**Core Process:**

1.  **Client Login/Registration**: Obtain a UID and a short-lived token from an external authentication service (simulated by a basic login API here).
2.  **Token Storage**: The server stores the token's hash, UID, and expiration time in the MySQL database.
3.  **Colyseus Connection Authentication**: Clients send their UID and token as connection options when attempting to connect to a Colyseus room.
4.  **Server Validation**: The Colyseus Room's `onAuth` method calls a custom `AuthService` to query and validate the token's validity and expiration from the database.
5.  **Connection Refusal/Acceptance**: The server rejects the connection if the token is invalid or expired; otherwise, it allows the client to join the room.

#### 1. Database Design and Setup

##### 1.1 `users` Table Structure (MySQL)

This table stores basic user information.

```sql
CREATE TABLE `users` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT COMMENT 'Unique User ID',
  `username` VARCHAR(255) NOT NULL UNIQUE COMMENT 'Username',
  `password_hash` VARCHAR(255) NOT NULL COMMENT 'Hashed password',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record Creation Time',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record Last Update Time',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores user accounts';
```

##### 1.2 `user_auth_tokens` Table Structure (MySQL)

This table stores user authentication token information.

```sql
CREATE TABLE `user_auth_tokens` (
  `uid` BIGINT UNSIGNED NOT NULL COMMENT 'Linked User ID from users table',
  `token` VARCHAR(64) NOT NULL COMMENT 'SHA-256 Game Token',
  `expires_at` BIGINT UNSIGNED NOT NULL COMMENT 'Token Expiration Timestamp (Unix milliseconds)',
  `created_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Record Creation Time',
  `updated_at` TIMESTAMP NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record Last Update Time',
  PRIMARY KEY (`uid`),
  UNIQUE KEY `idx_token` (`token`),
  FOREIGN KEY (`uid`) REFERENCES `users`(`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Stores active game login tokens';
```

##### 1.3 Database Connection Pool Configuration (`src/utils/db.ts`)

The `mysql2/promise` library provides a Promise-based MySQL connection pool for asynchronous operations.

```typescript
// src/utils/db.ts
import mysql from 'mysql2/promise';
import dotenv from 'dotenv';

dotenv.config();

const pool = mysql.createPool({
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: parseInt(process.env.DB_PORT || '3306', 10),
    waitForConnections: true,
    connectionLimit: 10,
    queueLimit: 0
});

pool.getConnection()
    .then(connection => {
        console.log('✅ MySQL 数据库连接成功！');
        connection.release();
    })
    .catch(err => {
        console.error('❌ 无法连接到 MySQL 数据库:', err.message);
        process.exit(1);
    });

export default pool;
```

##### 1.4 Environment Variables (`.env`)

Create a `.env` file in the project root to securely store database credentials.

```dotenv
# .env
DB_USER=your_mysql_username
DB_HOST=localhost
DB_NAME=your_mysql_database_name
DB_PASSWORD=your_mysql_password
DB_PORT=3306
```

#### 2. `AuthService` Implementation

`AuthService` encapsulates all user token-related business logic, including token generation, validation, and deletion.

##### `src/services/authService.ts`

```typescript
// src/services/authService.ts
import db from '../utils/db';
import crypto from 'crypto';
import mysql from 'mysql2/promise';

export class AuthService {
    /**
     * Generates a new token for a given user ID and stores its hash in the database.
     * @param uid User ID.
     * @param expiresInMinutes Token expiration in minutes.
     * @returns The raw token string.
     */
    public async generateToken(uid: string, expiresInMinutes: number = 60): Promise<string> {
        const rawToken = crypto.randomBytes(32).toString('hex');
        const hashedToken = crypto.createHash('sha256').update(rawToken).digest('hex');

        const expiresAt = Date.now() + (expiresInMinutes * 60 * 1000); // Unix milliseconds

        try {
            await db.execute(
                `INSERT INTO user_auth_tokens (uid, token, expires_at)
                 VALUES (?, ?, ?)
                 ON DUPLICATE KEY UPDATE
                 token = VALUES(token), expires_at = VALUES(expires_at), updated_at = CURRENT_TIMESTAMP;`,
                [uid, hashedToken, expiresAt]
            );
            console.log(`🔑 已为 UID: ${uid} 生成并存储新的令牌。`);
            return rawToken;
        } catch (error) {
            console.error(`❌ 为 UID ${uid} 生成和存储令牌失败:`, error);
            throw new Error('生成认证令牌失败。');
        }
    }

    /**
     * Verifies if a given token is valid and not expired for a user ID.
     * @param uid User ID.
     * @param token Raw token string from client.
     * @returns True if token is valid, false otherwise.
     */
    public async verifyToken(uid: string, token: string): Promise<boolean> {
        if (!uid || !token) {
            console.warn('⚠️ 尝试验证令牌时缺少用户ID或令牌。');
            return false;
        }

        const hashedToken = crypto.createHash('sha256').update(token).digest('hex');

        try {
            const [rows] = await db.execute<mysql.RowDataPacket[]>(
                `SELECT expires_at FROM user_auth_tokens WHERE uid = ? AND token = ?;`,
                [uid, hashedToken]
            );

            if (rows.length === 0) {
                console.warn(`❌ 令牌验证失败: 未找到 UID ${uid} 对应的匹配令牌。`);
                return false;
            }

            const expiresAt = Number(rows[0].expires_at); // Ensure it's a number
            const now = Date.now();

            if (expiresAt < now) {
                console.warn(`❌ 令牌验证失败: UID ${uid} 的令牌已过期。`);
                return false;
            }

            console.log(`✅ UID: ${uid} 的令牌验证成功。`);
            return true;
        } catch (error) {
            console.error(`❌ 验证 UID ${uid} 的令牌时发生错误:`, error);
            return false;
        }
    }

    /**
     * Deletes a user's token from the database.
     * @param uid User ID.
     */
    public async deleteToken(uid: string): Promise<void> {
        try {
            await db.execute(`DELETE FROM user_auth_tokens WHERE uid = ?;`, [uid]);
            console.log(`🗑️ 已删除 UID: ${uid} 的令牌。`);
        } catch (error) {
            console.error(`❌ 删除 UID ${uid} 的令牌失败:`, error);
            throw new Error('删除令牌失败。');
        }
    }
}

export const authService = new AuthService();
```

#### 3. Colyseus Room `onAuth` Integration

The `onAuth` method of a Colyseus Room is the entry point for client authentication, called when a client attempts to join a room.

##### `src/rooms/PublicLobbyRoom.ts` (Authentication part)

```typescript
// src/rooms/PublicLobbyRoom.ts (Excerpt for onAuth)
import { Room, Client } from '@colyseus/core';
import { Schema, type, MapSchema } from '@colyseus/schema';
import { PlayerState } from '../models/PlayerState';
import { ChatMessage } from '../models/ChatMessage';
import { authService } from '../services/authService'; // Use the actual authService

// ... (LobbyRoomState and other imports/definitions as in section 4.2)

export class PublicLobbyRoom extends Room<LobbyRoomState> {
    maxClients = 100;

    onCreate(options: any) {
        this.setState(new LobbyRoomState());
        console.log(`🎮 公共大厅 "${this.roomName}" (${this.roomId}) 已创建!`);

        this.onMessage("updatePlayerState", this.handlePlayerStateUpdate);
        this.onMessage("chat", this.handleChatMessage);
    }

    async onAuth(client: Client, options: { uid: string, token: string }): Promise<any> {
        console.log(`🔍 客户端 ${client.sessionId} 尝试认证 (UID: ${options?.uid || 'N/A'})...`);

        if (!options || !options.uid || !options.token) {
            console.warn(`❌ 认证失败: ${client.sessionId} 缺少UID或Token。`);
            throw new Error("认证失败：请提供用户ID和令牌。");
        }

        // Use the actual AuthService for token verification
        const isAuthenticated = await authService.verifyToken(options.uid, options.token);

        if (!isAuthenticated) {
            console.warn(`❌ 认证失败: UID ${options.uid} 的令牌无效或已过期。`);
            throw new Error("认证失败：令牌无效或已过期。");
        }

        console.log(`✅ 客户端 ${client.sessionId} (UID: ${options.uid}) 认证成功。`);
        // Return data to be passed to onJoin and client-side via `room.auth`
        return { uid: options.uid, isAuthenticated: true, /* ... more user data, e.g., username, avatar, etc. */ };
    }

    // ... (onJoin, handlePlayerStateUpdate, handleChatMessage, onLeave, onDispose methods)
}
```

#### 4. Server Entry File (`src/index.ts`) and Express API

This is the main entry point for the Colyseus server and a simple Express API for login. For production deployments with Colyseus Arena, `app.config.ts` (or `arena.config.ts`) would typically be used for server configuration. This `index.ts` demonstrates a direct setup.

```typescript
// src/index.ts
import http from 'http';
import express from 'express';
import cors from 'cors';
import { Server } from '@colyseus/core';
import { monitor } from '@colyseus/monitor';
import { WebSocketTransport } from "@colyseus/ws-transport";
import dotenv from 'dotenv';
import crypto from 'crypto'; // For password hashing simulation

import { PublicLobbyRoom } from './rooms/PublicLobbyRoom'; // Our main game room
import { authService } from './services/authService';
import db from './utils/db'; // Ensure DB is initialized

dotenv.config(); // Load environment variables

const port = Number(process.env.PORT || 2567);
const app = express();

app.use(cors());
app.use(express.json()); // Enable JSON body parsing

const gameServer = new Server({
    transport: new WebSocketTransport({
        server: http.createServer(app) // Use Express app to create HTTP server
    })
});

// Define our main game room
gameServer.define('public_lobby', PublicLobbyRoom);

// Colyseus monitoring panel
app.use("/colyseus", monitor());

// Basic login API for demonstration (In a real app, integrate with a full user management system)
app.post('/auth/login', async (req, res) => {
    const { username, password } = req.body;
    console.log(`Attempting login for username: ${username}`);

    if (!username || !password) {
        return res.status(400).json({ success: false, message: '缺少用户名或密码。' });
    }

    try {
        // In a real application:
        // 1. Query the 'users' table to get the user's ID and stored password hash.
        // 2. Use a secure hashing library (e.g., bcrypt) to compare the provided password with the stored hash.
        const [rows] = await db.execute(
            `SELECT id, password_hash FROM users WHERE username = ?;`,
            [username]
        );

        if (Array.isArray(rows) && rows.length === 0) {
            return res.status(401).json({ success: false, message: '用户名或密码不正确。' });
        }

        const user = rows[0] as { id: string, password_hash: string }; // Assuming id is BigInt from DB, convert to string for JS
        const inputPasswordHash = crypto.createHash('sha256').update(password).digest('hex'); // Simple hash for demo

        if (inputPasswordHash === user.password_hash) { // Compare hashes
            const token = await authService.generateToken(user.id, 60); // Generate token for 60 minutes
            return res.json({ success: true, uid: user.id, token, message: '登录成功，令牌已生成。' });
        } else {
            return res.status(401).json({ success: false, message: '用户名或密码不正确。' });
        }

    } catch (error: any) {
        console.error(`Error during login for ${username}:`, error);
        return res.status(500).json({ success: false, message: `登录失败: ${error.message}` });
    }
});

// Registration API for demonstration
app.post('/auth/register', async (req, res) => {
    const { username, password } = req.body;
    console.log(`Attempting registration for username: ${username}`);

    if (!username || !password) {
        return res.status(400).json({ success: false, message: '缺少用户名或密码。' });
    }
    if (password.length < 6) {
        return res.status(400).json({ success: false, message: '密码长度至少为6位。' });
    }

    try {
        const passwordHash = crypto.createHash('sha256').update(password).digest('hex'); // Simple hash for demo

        const [result] = await db.execute(
            `INSERT INTO users (username, password_hash) VALUES (?, ?);`,
            [username, passwordHash]
        );

        const newUserId = (result as any).insertId;
        console.log(`✅ User ${username} registered with ID: ${newUserId}`);
        return res.status(201).json({ success: true, message: '注册成功！', uid: newUserId });

    } catch (error: any) {
        if (error.code === 'ER_DUP_ENTRY') {
            return res.status(409).json({ success: false, message: '用户名已存在。' });
        }
        console.error(`Error during registration for ${username}:`, error);
        return res.status(500).json({ success: false, message: `注册失败: ${error.message}` });
    }
});


gameServer.listen(port)
    .then(() => {
        console.log(`🚀 Colyseus 服务器已启动，监听 ws://localhost:${port}`);
        console.log(`🌐 Colyseus 监控面板: http://localhost:${port}/colyseus`);
        console.log(`🔑 认证 API 示例: POST http://localhost:${port}/auth/register (注册), POST http://localhost:${port}/auth/login (登录)`);
    })
    .catch((err) => {
        console.error("❌ Colyseus 服务器启动失败:", err);
        process.exit(1);
    });
```

### 5. Deployment and Running

#### 5.1 Install Dependencies

```bash
# Install production dependencies
npm install express colyseus @colyseus/monitor @colyseus/ws-transport mysql2 dotenv uuid

# Install development dependencies (TypeScript related)
npm install -D typescript ts-node @types/express @types/mysql2 @types/node @types/cors @types/uuid
```

#### 5.2 Configure `tsconfig.json`

Ensure your TypeScript compiler is correctly configured.

```json
{
  "compilerOptions": {
    "target": "es2020",
    "module": "commonjs",
    "outDir": "./dist",
    "rootDir": "./src",
    "strict": true,
    "esModuleInterop": true,
    "skipLibCheck": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true
  },
  "include": ["src/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 4.2 Public Lobby (PublicLobbyRoom) Design

This design covers the core structure of a Colyseus room, synchronizable player states, real-time player state synchronization, and a basic in-room chat system. It uses `AuthService` for player authentication upon joining.

#### 1. Player State Schema (`src/models/PlayerState.ts`)

`@colyseus/schema` is used to define the player data structure that can be synchronized within the room.

```typescript
// src/models/PlayerState.ts
import { Schema, type, ArraySchema } from '@colyseus/schema';

export class PlayerState extends Schema {
    @type("string") sessionId: string;
    @type("string") uid: string; // User ID from authentication
    @type("number") x: number = 0;
    @type("number") y: number = 0;
    @type("number") dir: number = 0; // Direction (e.g., 0: up, 1: down, etc.)
    @type("boolean") isFlipped: boolean = false; // For sprite flipping
    @type("boolean") isSitting: boolean = false;
    @type("string") currentAnimation: string = "idle";
    @type([ "string" ]) currentOutfit = new ArraySchema<string>(); // Array of item IDs for outfit
    // Add other player-specific states as needed (e.g., username, avatar_id)

    constructor(sessionId: string, uid: string) {
        super();
        this.sessionId = sessionId;
        this.uid = uid;
    }
}
```

#### 2. Chat Message Schema (`src/models/ChatMessage.ts`)

Defines the data structure for chat messages.

```typescript
// src/models/ChatMessage.ts
import { Schema, type } from '@colyseus/schema';

export class ChatMessage extends Schema {
    @type("string") senderSessionId: string;
    @type("string") senderUid: string; // User ID of the sender
    @type("string") message: string;
    @type("number") timestamp: number; // Unix milliseconds
    @type("string") channel: string; // e.g., "lobby", "private"

    constructor(senderSessionId: string, senderUid: string, message: string, channel: string = "lobby") {
        super();
        this.senderSessionId = senderSessionId;
        this.senderUid = senderUid;
        this.message = message;
        this.timestamp = Date.now();
        this.channel = channel;
    }
}
```

#### 3. Public Lobby Room (`src/rooms/PublicLobbyRoom.ts`)

This contains the core room logic, including state management, player synchronization, and chat handling. It uses the `AuthService` for `onAuth`.

```typescript
// src/rooms/PublicLobbyRoom.ts
import { Room, Client } from '@colyseus/core';
import { Schema, type, MapSchema } from '@colyseus/schema';
import { PlayerState } from '../models/PlayerState';
import { ChatMessage } from '../models/ChatMessage';
import { authService } from '../services/authService'; // Use the actual authService

// Defines the synchronizable state structure for the PublicLobbyRoom
export class LobbyRoomState extends Schema {
    @type({ map: PlayerState }) players = new MapSchema<PlayerState>();
    @type("number") playerCount: number = 0;
}

export class PublicLobbyRoom extends Room<LobbyRoomState> {
    maxClients = 100; // Maximum number of clients allowed in this room

    onCreate(options: any) {
        this.setState(new LobbyRoomState()); // Initialize the room state
        console.log(`🎮 公共大厅 "${this.roomName}" (${this.roomId}) 已创建!`);

        // Register message handlers
        this.onMessage("updatePlayerState", this.handlePlayerStateUpdate);
        this.onMessage("chat", this.handleChatMessage);
    }

    /**
     * Called when a client attempts to join the room for authentication.
     * @param client The joining client.
     * @param options Options passed from the client's `join()` call.
     * @returns Arbitrary data to be passed to onJoin and available on client via `room.auth`.
     */
    async onAuth(client: Client, options: { uid: string, token: string }): Promise<any> {
        console.log(`🔍 客户端 ${client.sessionId} 尝试认证 (UID: ${options?.uid || 'N/A'})...`);

        if (!options || !options.uid || !options.token) {
            console.warn(`❌ 认证失败: ${client.sessionId} 缺少用户ID或令牌。`);
            throw new Error("认证失败：请提供用户ID和令牌。");
        }

        // Use the centralized AuthService for token verification
        const isAuthenticated = await authService.verifyToken(options.uid, options.token);

        if (!isAuthenticated) {
            console.warn(`❌ 认证失败: UID ${options.uid} 的令牌无效或已过期。`);
            throw new Error("认证失败：令牌无效或已过期。");
        }

        console.log(`✅ 客户端 ${client.sessionId} (UID: ${options.uid}) 认证成功。`);
        return { uid: options.uid, isAuthenticated: true, /* ... more user data, e.g., username, avatar_id */ };
    }

    /**
     * Called when a client successfully joins the room.
     * @param client The joined client.
     * @param options Options passed from the client's `join()` call.
     * @param auth The data returned from `onAuth`.
     */
    onJoin(client: Client, options: any, auth: { uid: string, isAuthenticated: boolean }) {
        console.log(`➡️ 客户端 ${client.sessionId} (UID: ${auth.uid}) 已加入房间 "${this.roomName}"。`);

        // Create a new PlayerState for the joining client
        const player = new PlayerState(client.sessionId, auth.uid);
        // Set initial random position for demonstration
        player.x = Math.random() * 800;
        player.y = Math.random() * 600;
        player.currentOutfit.push("hat_01", "shirt_blue"); // Example outfit

        this.state.players.set(client.sessionId, player); // Add player to the room state
        this.state.playerCount = this.state.players.size; // Update player count

        // Send a welcome message to the joining client
        client.send("welcome", { message: `欢迎来到大厅, ${auth.uid}!` });

        // Broadcast a message to all other clients that a new player joined
        this.broadcast("player_joined", { sessionId: client.sessionId, uid: auth.uid }, { except: client });
    }

    /**
     * Handles incoming "updatePlayerState" messages from clients.
     * Updates the player's position, animation, or other attributes.
     * @param client The client sending the message.
     * @param data The player state update data.
     */
    handlePlayerStateUpdate(client: Client, data: any) {
        const player = this.state.players.get(client.sessionId);
        if (player) {
            if (typeof data.x === 'number') player.x = data.x;
            if (typeof data.y === 'number') player.y = data.y;
            if (typeof data.dir === 'number') player.dir = data.dir;
            if (typeof data.isFlipped === 'boolean') player.isFlipped = data.isFlipped;
            if (typeof data.isSitting === 'boolean') player.isSitting = data.isSitting;
            if (typeof data.currentAnimation === 'string') player.currentAnimation = data.currentAnimation;

            if (Array.isArray(data.currentOutfit)) {
                player.currentOutfit.clear(); // Clear existing outfit
                data.currentOutfit.forEach((itemId: string) => player.currentOutfit.push(itemId)); // Add new outfit items
            }
        }
    }

    /**
     * Handles incoming "chat" messages from clients.
     * Broadcasts the message to all clients in the room.
     * @param client The client sending the message.
     * @param messageText The chat message content.
     */
    handleChatMessage(client: Client, messageText: string) {
        const player = this.state.players.get(client.sessionId);
        if (!player) {
            console.warn(`[Chat] 收到来自未知客户端 ${client.sessionId} 的聊天消息。`);
            return;
        }

        console.log(`[Chat] ${player.uid} (${client.sessionId}): ${messageText}`);

        const chatMessage = new ChatMessage(client.sessionId, player.uid, messageText, "lobby");

        this.broadcast("chat", chatMessage); // Broadcast the chat message to all clients
    }

    /**
     * Called when a client leaves the room.
     * @param client The client who left.
     * @param consented True if the client left voluntarily, false otherwise (e.g., disconnection).
     */
    onLeave(client: Client, consented: boolean) {
        console.log(`⬅️ 客户端 ${client.sessionId} (UID: ${this.state.players.get(client.sessionId)?.uid || 'N/A'}) 离开了房间。自愿离开: ${consented}`);

        this.state.players.delete(client.sessionId); // Remove player from the room state
        this.state.playerCount = this.state.players.size; // Update player count

        this.broadcast("player_left", { sessionId: client.sessionId }); // Notify other clients
    }

    /**
     * Called when the room is disposed (e.g., no clients left, or server shutdown).
     */
    onDispose() {
        console.log(`🗑️ 公共大厅 "${this.roomName}" (${this.roomId}) 已销毁。`);
    }
}
```

### 4.3 Friend System

#### 1. Database Schema

The `friend_relationships` table stores the friendship status between users.

```sql
CREATE TABLE friend_relationships (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    user_one_id BIGINT UNSIGNED NOT NULL COMMENT 'Smaller user ID, ensures uniqueness',
    user_two_id BIGINT UNSIGNED NOT NULL COMMENT 'Larger user ID, ensures uniqueness',
    status ENUM('pending', 'accepted', 'declined', 'blocked', 'deleted') NOT NULL COMMENT 'Relationship status: pending, accepted, declined, blocked, deleted',
    action_user_id BIGINT UNSIGNED NOT NULL COMMENT 'User ID who performed the last action on this relationship',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Record creation time',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Record last update time',
    UNIQUE KEY idx_unique_relationship (user_one_id, user_two_id),
    FOREIGN KEY (user_one_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (user_two_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (action_user_id) REFERENCES users(id) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE INDEX idx_user_id_one ON friend_relationships (user_one_id);
CREATE INDEX idx_user_id_two ON friend_relationships (user_two_id);
```

**Design Notes:**

* **`user_one_id`, `user_two_id`**: `user_one_id` is always the smaller of the two user IDs, and `user_two_id` is the larger, ensuring a single record per pair. This simplifies querying and prevents duplicate friendship entries.
* **`status`**:
    * `pending`: Friend request sent, awaiting response.
    * `accepted`: Friend request accepted, users are friends.
    * `declined`: Friend request declined.
    * `blocked`: One user has blocked the other. Blocking is unidirectional.
    * `deleted`: Friendship has been removed. This allows historical tracking or soft-delete.
* **`action_user_id`**: Records the user ID who last updated the relationship status. This is crucial for distinguishing who initiated a `pending` request or `blocked` status.

#### 2. API Endpoint Design

All requests should require user authentication, with the current user's `userId` (a `BIGINT` from the database, likely handled as a `string` in JSON APIs) extracted from the authentication information (e.g., JWT).

##### 2.1 Send Friend Request

* **Endpoint**: `POST /api/friends/request`
* **Description**: Current user sends a friend request to a target user.
* **Request Body**:
    ```json
    {
        "targetUserId": "12345" // User ID of the recipient
    }
    ```
* **Responses**:
    * **Success (200 OK)**:
        ```json
        {
            "success": true,
            "message": "好友请求已发送。"
        }
        ```
    * **Error (400 Bad Request)**: Examples include `{"success": false, "message": "不能向自己发送好友请求。"}` or `{"success": false, "message": "好友请求已存在或已是好友。"}`.
    * **Error (401 Unauthorized)**: Authentication token missing or invalid.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `currentUserId` from the authenticated user.
    2.  Validate `targetUserId` (must be a valid user and not the `currentUserId`).
    3.  Normalize `currentUserId` and `targetUserId` into `user_one_id` and `user_two_id` (smaller, larger).
    4.  Check for existing relationships in `friend_relationships` between `currentUserId` and `targetUserId`.
    5.  If a `pending` request from `currentUserId` to `targetUserId` already exists, return appropriate error.
    6.  If they are already `accepted` friends, return appropriate error.
    7.  If the `targetUserId` has blocked `currentUserId`, the request may fail or be soft-ignored, depending on privacy policy.
    8.  Insert a new record with `status = 'pending'` and `action_user_id = currentUserId`, or update an existing `declined`/`deleted` record.
    9.  Notify `targetUserId` (via WebSocket if online) of the new request.

##### 2.2 Respond to Friend Request

* **Endpoint**: `POST /api/friends/response`
* **Description**: Current user accepts or declines a received friend request.
* **Request Body**:
    ```json
    {
        "senderUserId": "67890", // User ID of the request sender
        "response": "accept"   // "accept" or "decline"
    }
    ```
* **Responses**:
    * **Success (200 OK)**: `{"success": true, "message": "好友请求已接受。"}` or `{"success": true, "message": "好友请求已拒绝。"}`.
    * **Error (400 Bad Request)**: `{"success": false, "message": "无效的响应。"}` or `{"success": false, "message": "好友请求不存在或状态不正确。"}`.
    * **Error (401 Unauthorized)**: Authentication token missing or invalid.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `currentUserId` from authentication.
    2.  Validate `senderUserId` and `response` ("accept" or "decline").
    3.  Normalize `currentUserId` and `senderUserId` into `user_one_id` and `user_two_id`.
    4.  Find the pending request in `friend_relationships` where the current user is the recipient (`user_two_id`) and `action_user_id` is `senderUserId`.
    5.  If found and `status` is `pending`:
        * If `response` is "accept", update `status` to `accepted` and set `action_user_id` to `currentUserId`.
        * If `response` is "decline", update `status` to `declined` and set `action_user_id` to `currentUserId`.
    6.  Notify `senderUserId` (via WebSocket if online) of the response.

##### 2.3 List Friends and Requests

* **Endpoint**: `GET /api/friends`
* **Description**: Lists all friends and pending requests for the current user.
* **Request Parameters**:
    * `status`: Optional. Filter by relationship status (e.g., `accepted`, `pending`, `blocked`). If not provided, returns all categories.
* **Responses**:
    * **Success (200 OK)**:
        ```json
        {
            "success": true,
            "data": {
                "acceptedFriends": [ /* Array of { uid: "string", username: "string", isOnline: boolean } */ ],
                "sentRequests": [ /* Array of { uid: "string", username: "string" } */ ],
                "receivedRequests": [ /* Array of { uid: "string", username: "string" } */ ],
                "blockedUsers": [ /* Array of { uid: "string", username: "string" } */ ]
            }
        }
        ```
    * **Error (401 Unauthorized)**: Authentication token missing or invalid.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `currentUserId`.
    2.  Query `friend_relationships` for records where `user_one_id` or `user_two_id` is `currentUserId`.
    3.  For each record, determine the "other" user's ID.
    4.  Categorize relationships based on `status` and `action_user_id`.
        * `accepted`: `status = 'accepted'`.
        * `sentRequests`: `status = 'pending'` and `action_user_id = currentUserId`.
        * `receivedRequests`: `status = 'pending'` and `action_user_id != currentUserId`.
        * `blockedUsers`: `status = 'blocked'` and `action_user_id = currentUserId`.
    5.  Retrieve basic public user information (like username) for each related user ID.
    6.  Optionally integrate with game server presence to determine `isOnline` status.

##### 2.4 Remove Friend / Unblock

* **Endpoint**: `POST /api/friends/remove`
* **Description**: Current user removes a friendship or unblocks a user. This changes the status to 'deleted'.
* **Request Body**:
    ```json
    {
        "targetUserId": "12345" // User ID to remove/unblock
    }
    ```
* **Responses**:
    * **Success (200 OK)**: `{"success": true, "message": "好友关系已解除。"}`.
    * **Error (400 Bad Request)**: `{"success": false, "message": "关系不存在或无法操作。"}`.
    * **Error (401 Unauthorized)**: Authentication token missing or invalid.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `currentUserId`.
    2.  Validate `targetUserId`.
    3.  Normalize `currentUserId` and `targetUserId`.
    4.  Find the corresponding record in `friend_relationships`.
    5.  If found, update `status` to `deleted` and `action_user_id` to `currentUserId`.
    6.  Notify `targetUserId` if online about the relationship change.

#### 3. Other Considerations

* **API Prefix**: All friend system APIs are suggested to be under a unified `/api/friends` path for clarity.
* **Real-time Updates**: Implement WebSocket communication for real-time notifications (e.g., new friend requests, status changes) to ensure a dynamic user experience.
* **Idempotency**: Repeated friend requests from the same user to the same target should not create duplicate records, but update the existing one if allowed.
* **User Privacy**: Ensure only necessary public user information is returned.
* **Transactions**: Use database transactions for multi-operation logic (e.g., updating a relationship and then potentially adding a new one).
* **Blocking Logic**: When user A blocks user B, future requests from B to A might be automatically declined or hidden. This is unidirectional.
* **Pagination**: Implement pagination for interfaces that may return large amounts of data (e.g., large friend lists).
* **Indexing**: Ensure appropriate indexes on `friend_relationships` (`user_one_id`, `user_two_id`) for query optimization.
* **Security**: All APIs must undergo strict authentication (using the `AuthService` and JWT) and authorization checks.

### 4.4 Character Management System

#### 1. Database Schema

The `characters` table stores all player character information, including basic attributes, appearance, and last known location.

```sql
CREATE TABLE characters (
    character_id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY COMMENT 'Unique character ID',
    user_id BIGINT UNSIGNED NOT NULL COMMENT 'User ID owning the character',
    character_name VARCHAR(50) NOT NULL UNIQUE COMMENT 'Character name, globally unique',
    level INT DEFAULT 1 NOT NULL COMMENT 'Character level',
    experience BIGINT DEFAULT 0 NOT NULL COMMENT 'Character experience points',
    pos_x FLOAT DEFAULT 0.0 NOT NULL COMMENT 'Last saved X coordinate',
    pos_y FLOAT DEFAULT 0.0 NOT NULL COMMENT 'Last saved Y coordinate',
    current_scene_id VARCHAR(255) DEFAULT 'public_lobby' NOT NULL COMMENT 'Last known scene ID (e.g.: public_lobby, private_home_ownerId)',
    selected_appearance_details JSON NOT NULL COMMENT 'Character appearance details, JSON format',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT 'Character creation time',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Last character update time',

    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_character_name (character_name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

**Design Notes:**

* **`character_id`**: Primary key, unique identifier for each character.
* **`user_id`**: Foreign key, links to the user who owns the character.
* **`character_name`**: Globally unique character name.
* **`level`, `experience`**: Basic character progression attributes.
* **`pos_x`, `pos_y`, `current_scene_id`**: Records the character's last known position and scene for state restoration on login. `current_scene_id` can be a Colyseus room name or a custom identifier for private spaces.
* **`selected_appearance_details`**: JSON type for flexible storage of complex and frequently changing character appearance data. This allows for dynamic and extensible appearance options without frequent schema changes.

#### 2. API Endpoint Design

All requests should require user authentication, with the current user's `userId` extracted from the authentication information. The `userId` is a `BIGINT` in the database, but for consistency in JSON APIs, it might be represented as a `string` (or `number` if within safe integer limits). We'll use `string` for API examples here.

##### 2.1 Create New Character

* **Endpoint**: `POST /api/characters`
* **Description**: Creates a new character for the current user.
* **Request Body**:
    ```json
    {
        "characterName": "勇敢的小猫咪",
        "initialAppearanceDetails": {
            "body_type_id": "human_male_01",
            "face_id": "face_cute_01",
            "hair_id": "hair_spiky_brown",
            "skin_tone_id": "skin_fair",
            "initial_outfit": {
                "head_item_id": "helmet_knight",
                "upper_body_item_id": "armor_plate",
                "lower_body_item_id": "pants_blue_jeans",
                "feet_item_id": "shoes_sneakers"
            }
        }
    }
    ```
* **Responses**:
    * **Success (201 Created)**: `{"success": true, "message": "角色创建成功。", "characterId": "1001"}`.
    * **Error (400 Bad Request)**: Examples include `{"success": false, "message": "角色名称已存在。"}` or `{"success": false, "message": "每个用户最多只能创建X个角色。"}` (e.g., `max_characters_per_user` rule).
    * **Error (401 Unauthorized)**: Unauthenticated user.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `userId` from authentication token.
    2.  Validate `characterName` (e.g., length, forbidden words, uniqueness).
    3.  Validate `initialAppearanceDetails` against predefined valid appearance resource IDs (server-side check).
    4.  Check if `userId` has reached their character creation limit.
    5.  Insert new record into `characters` table with default `level`, `experience`, `pos_x`, `pos_y`, `current_scene_id`, and the provided `characterName` and `initialAppearanceDetails`.
    6.  Return the `character_id` of the newly created character.

##### 2.2 List User Characters

* **Endpoint**: `GET /api/characters`
* **Description**: Retrieves a list of all characters owned by the current user, providing essential information for character selection.
* **Request Parameters**: None.
* **Responses**:
    * **Success (200 OK)**:
        ```json
        {
            "success": true,
            "characters": [
                {
                    "characterId": "1001",
                    "characterName": "勇敢的小猫咪",
                    "level": 1,
                    "currentSceneId": "public_lobby",
                    "basicAppearance": { // Simplified appearance for display
                        "body_type_id": "human_male_01",
                        "face_id": "face_cute_01",
                        "hair_id": "hair_spiky_brown"
                    },
                    "createdAt": "2024-01-01T10:00:00Z"
                },
                {
                    "characterId": "1002",
                    "characterName": "神秘的狐狸",
                    "level": 5,
                    "currentSceneId": "private_home_user99",
                    "basicAppearance": {
                        "body_type_id": "human_female_01",
                        "face_id": "face_elegant_02",
                        "hair_id": "hair_long_blonde"
                    },
                    "createdAt": "2024-01-05T15:30:00Z"
                }
            ]
        }
        ```
    * **Error (401 Unauthorized)**: Unauthenticated user.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `userId`.
    2.  Query `characters` table for records where `user_id` matches `currentUserId`.
    3.  Extract and return selected fields (e.g., `character_id`, `character_name`, `level`, `current_scene_id`, and relevant parts of `selected_appearance_details` for a quick preview).
    4.  Sort characters by `created_at` or `updated_at`.

##### 2.3 Load Full Character Data

* **Endpoint**: `GET /api/characters/{character_id}`
* **Description**: Retrieves all detailed data for a specified character, typically used when a player selects a character to enter the game.
* **Request Path Parameters**: `character_id` (e.g., `/api/characters/1001`).
* **Responses**:
    * **Success (200 OK)**:
        ```json
        {
            "success": true,
            "character": {
                "characterId": "1001",
                "characterName": "勇敢的小猫咪",
                "userId": "54321",
                "level": 1,
                "experience": 0,
                "posX": 123.45,
                "posY": 678.90,
                "currentSceneId": "public_lobby",
                "selectedAppearanceDetails": {
                    "body_type_id": "human_male_01",
                    "face_id": "face_cute_01",
                    "hair_id": "hair_spiky_brown",
                    "skin_tone_id": "skin_fair",
                    "initial_outfit": {
                        "head_item_id": "helmet_knight",
                        "upper_body_item_id": "armor_plate",
                        "lower_body_item_id": "pants_blue_jeans",
                        "feet_item_id": "shoes_sneakers"
                    }
                },
                "createdAt": "2024-01-01T10:00:00Z",
                "updatedAt": "2024-01-01T10:00:00Z"
            }
        }
        ```
    * **Error (401 Unauthorized)**: Unauthenticated user.
    * **Error (403 Forbidden)**: Attempt to load another user's character.
    * **Error (404 Not Found)**: Character does not exist.
    * **Error (500 Internal Server Error)**: Server internal error.
* **Operation Logic**:
    1.  Get `userId`.
    2.  Get `character_id` from path parameters.
    3.  Query `characters` table for the matching `character_id`.
    4.  Validate character existence and ownership (ensure `user_id` matches `currentUserId`). If not owned by the current user, return 403 Forbidden.
    5.  Return all detailed character information, including the full `selected_appearance_details` JSON.

#### 3. Integration and Considerations

* **Authentication**: All API endpoints rely on the robust authentication mechanism provided by `AuthService`.
* **API Prefix**: All character management APIs are suggested to be under a unified `/api/characters` path.
* **Transactions**: Use database transactions for complex logic involving multiple database operations to ensure data consistency.
* **Error Messages**: Return clear and specific error messages for better client-side debugging.
* **Client Synchronization**: When a player selects a character and joins a Colyseus room, the game server (e.g., `PublicLobbyRoom`) should load this character data from the database and use it to initialize the `PlayerState` for synchronization to other clients.
* **Updating Character Data**: Future `PUT` or `PATCH /api/characters/{character_id}` endpoints would be used for updating a character's `level`, `experience`, `pos_x`, `pos_y`, `current_scene_id`, or `selected_appearance_details`. These would also require ownership validation.
* **Appearance Data Validation**: The server must rigorously validate `initialAppearanceDetails` (and any subsequent updates) against a master list of valid and available appearance resource IDs to prevent invalid client data from being stored.

### 4.5 Private Space Management (Home and Garden)

This design provides customizable private spaces (home and garden) for each user, including furniture placement, plant cultivation and growth, and visitor access management.

#### 1. Core Schema Definitions (`src/models/`)

These schemas will be defined for various room state synchronization within Colyseus.

##### 1.1 `HomeItemState.ts` (Home Item State)

Represents furniture or decorative items placed in the home.

```typescript
// src/models/HomeItemState.ts
import { Schema, type } from '@colyseus/schema';

export class HomeItemState extends Schema {
    @type("string") instanceId: string; // Unique instance ID (UUID)
    @type("string") templateId: string; // ID of the item template (e.g., "chair_wooden")
    @type("number") x: number = 0;
    @type("number") y: number = 0;
    @type("number") rotation: number = 0; // Rotation angle (0-360)
    @type("boolean") isFlipped: boolean = false; // Horizontal flip
}
```

##### 1.2 `GardenPlotState.ts` (Garden Plot State)

Represents a plantable plot in the garden.

```typescript
// src/models/GardenPlotState.ts
import { Schema, type } from '@colyseus/schema';

export class GardenPlotState extends Schema {
    @type("string") plotId: string; // Unique ID for this plot (e.g., "plot_001")
    @type("string") plotTemplateId: string; // Type of plot (e.g., "basic_soil_plot")
    @type("string") seedId: string | null = null; // ID of the currently planted seed, null if empty
    @type("number") plantTimestamp: number = 0; // Unix milliseconds when seed was planted
    @type("number") growthStage: number = 0; // Current growth stage (0 to max_stages)
    @type("number") lastWateredTimestamp: number = 0; // Unix milliseconds when last watered
}
```

##### 1.3 `HomeRoomState.ts` (Home Room State)

Defines the `HomeRoom`'s state, synchronizing the owner's home environment.

```typescript
// src/models/HomeRoomState.ts
import { Schema, type, MapSchema } from '@colyseus/schema';
import { PlayerState } from './PlayerState'; // Reusing PlayerState for visitors
import { HomeItemState } from './HomeItemState';

export class HomeRoomState extends Schema {
    @type("string") ownerUid: string; // User ID of the home owner
    @type("string") homeBackgroundId: string = "default_home_bg"; // Current home background/theme
    @type({ map: PlayerState }) players = new MapSchema<PlayerState>(); // Players currently in the home
    @type({ map: HomeItemState }) furniture = new MapSchema<HomeItemState>(); // Placed furniture items
    @type("string") accessLevel: string = "private"; // Read-only for clients, indicates current room access level
}
```

##### 1.4 `GardenRoomState.ts` (Garden Room State)

Defines the `GardenRoom`'s state, synchronizing the owner's garden environment.

```typescript
// src/models/GardenRoomState.ts
import { Schema, type, MapSchema } from '@colyseus/schema';
import { PlayerState } from './PlayerState'; // Reusing PlayerState for visitors
import { GardenPlotState } from './GardenPlotState';

export class GardenRoomState extends Schema {
    @type("string") ownerUid: string; // User ID of the garden owner
    @type("string") gardenBackgroundId: string = "default_garden_bg"; // Current garden background/theme
    @type({ map: PlayerState }) players = new MapSchema<PlayerState>(); // Players currently in the garden
    @type({ map: GardenPlotState }) plots = new MapSchema<GardenPlotState>(); // Planted garden plots
    @type("string") accessLevel: string = "private"; // Read-only for clients, indicates current room access level
}
```

#### 2. Database Schema (MySQL)

##### 2.1 `user_private_spaces` Table

Stores basic configurations and access levels for users' private spaces (home and garden).

```sql
CREATE TABLE user_private_spaces (
    owner_user_id BIGINT UNSIGNED PRIMARY KEY COMMENT 'Linked to users table ID',
    home_background_id VARCHAR(255) DEFAULT 'default_home_bg' NOT NULL COMMENT 'Home background item ID',
    garden_background_id VARCHAR(255) DEFAULT 'default_garden_bg' NOT NULL COMMENT 'Garden background item ID',
    home_access_level ENUM('private', 'friends_only', 'public') DEFAULT 'private' NOT NULL COMMENT 'Home access level',
    garden_access_level ENUM('private', 'friends_only', 'public') DEFAULT 'private' NOT NULL COMMENT 'Garden access level',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_user_id) REFERENCES users(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

##### 2.2 `home_items` Table (Home Furniture)

Stores instances of furniture placed within a user's home.

```sql
CREATE TABLE home_items (
    item_instance_id VARCHAR(36) PRIMARY KEY COMMENT 'Furniture instance UUID',
    owner_user_id BIGINT UNSIGNED NOT NULL COMMENT 'User ID owning the furniture',
    item_template_id VARCHAR(255) NOT NULL COMMENT 'Furniture template ID (e.g.: "chair_wooden")',
    pos_x FLOAT NOT NULL COMMENT 'X coordinate',
    pos_y FLOAT NOT NULL COMMENT 'Y coordinate',
    rotation FLOAT DEFAULT 0.0 NOT NULL COMMENT 'Rotation angle (0-360 degrees)',
    is_flipped BOOLEAN DEFAULT FALSE NOT NULL COMMENT 'Whether the item is horizontally flipped',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_owner_user_id (owner_user_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

##### 2.3 `garden_plots` Table (Garden Plots)

Stores the state of each plot in a user's garden. Each plot has a unique ID within a user's garden.

```sql
CREATE TABLE garden_plots (
    plot_id VARCHAR(36) PRIMARY KEY COMMENT 'Unique plot ID (e.g.: "garden_plot_A1")',
    owner_user_id BIGINT UNSIGNED NOT NULL COMMENT 'User ID owning the plot',
    plot_template_id VARCHAR(255) NOT NULL COMMENT 'Plot template ID (e.g.: "basic_garden_plot")',
    seed_id VARCHAR(255) NULL COMMENT 'Currently planted seed ID, NULL if not planted',
    plant_timestamp BIGINT UNSIGNED DEFAULT 0 NOT NULL COMMENT 'Planting timestamp (Unix milliseconds)',
    growth_stage INT DEFAULT 0 NOT NULL COMMENT 'Current growth stage (0-max_stages)',
    last_watered_timestamp BIGINT UNSIGNED DEFAULT 0 NOT NULL COMMENT 'Last watered timestamp (Unix milliseconds)',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (owner_user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE KEY idx_owner_plot (owner_user_id, plot_id) -- Ensures unique plot for an owner
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

##### 2.4 `items` Table (Item Template Definition)

This table defines all possible item templates in the game, extended with specific fields for plant items.

```sql
CREATE TABLE items (
    item_id VARCHAR(255) PRIMARY KEY COMMENT 'Unique Item template ID (e.g.: "chair_wooden", "seed_carrot")',
    item_name VARCHAR(255) NOT NULL COMMENT 'Item name',
    item_type ENUM('furniture', 'seed', 'plant', 'collectible', 'consumable', 'background') NOT NULL COMMENT 'Item type classification',
    description TEXT NULL COMMENT 'Item description',
    is_stackable BOOLEAN DEFAULT FALSE NOT NULL,
    max_stack_size INT DEFAULT 1 NOT NULL,
    price INT DEFAULT 0 NOT NULL COMMENT 'Purchase price (e.g., in game currency)',
    total_growth_time_ms BIGINT UNSIGNED DEFAULT 0 COMMENT 'Total growth time required for plants in milliseconds',
    num_growth_stages INT DEFAULT 1 COMMENT 'Number of plant growth stages',
    water_interval_ms BIGINT UNSIGNED DEFAULT 0 COMMENT 'Time interval for plant watering (milliseconds)',
    harvest_details JSON NULL COMMENT 'Harvest details for plants, JSON format, e.g.: [{"item_id": "carrot", "quantity": 1}, {"item_id": "seed_carrot", "quantity": 1}]',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
```

#### 3. Service Layer (`src/services/`)

These services encapsulate business logic and database interactions.

##### 3.1 `ItemService.ts`

Handles retrieving item template details from the `items` table.

```typescript
// src/services/ItemService.ts
import db from '../utils/db';
import { RowDataPacket } from 'mysql2/promise';

export class ItemService {
    private static instance: ItemService;

    public static getInstance(): ItemService {
        if (!ItemService.instance) {
            ItemService.instance = new ItemService();
        }
        return ItemService.instance;
    }

    private constructor() {}

    /**
     * Retrieves details for a specific item template.
     * @param itemId The item template ID.
     * @returns Item details or null if not found.
     */
    public async getItemDetails(itemId: string): Promise<RowDataPacket | null> {
        const [rows] = await db.execute<RowDataPacket[]>(
            `SELECT * FROM items WHERE item_id = ?;`,
            [itemId]
        );
        return rows.length > 0 ? rows[0] : null;
    }
}
```

##### 3.2 `UserService.ts`

A placeholder service for retrieving basic user information (e.g., username).

```typescript
// src/services/UserService.ts
import db from '../utils/db';
import { RowDataPacket } from 'mysql2/promise';

export class UserService {
    private static instance: UserService;

    public static getInstance(): UserService {
        if (!UserService.instance) {
            UserService.instance = new UserService();
        }
        return UserService.instance;
    }

    private constructor() {}

    /**
     * Retrieves basic user information by user ID.
     * @param userId The user's ID.
     * @returns User data (e.g., username) or null if not found.
     */
    public async getUserBasicInfo(userId: string): Promise<{ uid: string, username: string } | null> {
        const [rows] = await db.execute<RowDataPacket[]>(
            `SELECT id AS uid, username FROM users WHERE id = ?;`,
            [userId]
        );
        return rows.length > 0 ? { uid: String(rows[0].uid), username: rows[0].username } : null;
    }
}
```

##### 3.3 `FriendService.ts`

A placeholder service for friend relationship checks (used by `SpaceService` for access control).

```typescript
// src/services/FriendService.ts
import db from '../utils/db';
import { RowDataPacket } from 'mysql2/promise';

export class FriendService {
    private static instance: FriendService;

    public static getInstance(): FriendService {
        if (!FriendService.instance) {
            FriendService.instance = new FriendService();
        }
        return FriendService.instance;
    }

    private constructor() {}

    /**
     * Checks if two users are friends.
     * @param userId1 First user ID.
     * @param userId2 Second user ID.
     * @returns True if they are friends, false otherwise.
     */
    public async areFriends(userId1: string, userId2: string): Promise<boolean> {
        if (userId1 === userId2) return true; // A user is always "friends" with themselves for private space access

        const sortedUids = [userId1, userId2].sort();
        const userOne = sortedUids[0];
        const userTwo = sortedUids[1];

        const [rows] = await db.execute<RowDataPacket[]>(
            `SELECT status FROM friend_relationships WHERE user_one_id = ? AND user_two_id = ?;`,
            [userOne, userTwo]
        );

        return rows.length > 0 && rows[0].status === 'accepted';
    }
}
```

##### 3.4 `SpaceService.ts`

Handles all database operations related to private spaces (home and garden).

```typescript
// src/services/SpaceService.ts
import db from '../utils/db';
import { RowDataPacket } from 'mysql2/promise';
import { HomeItemState } from '../models/HomeItemState';
import { GardenPlotState } from '../models/GardenPlotState';
import { v4 as uuidv4 } from 'uuid'; // For generating unique IDs for items/plots

import { ItemService } from './ItemService';
import { UserService } from './UserService';
import { FriendService } from './FriendService';

export interface PrivateSpaceSettings {
    ownerUserId: string;
    homeBackgroundId: string;
    gardenBackgroundId: string;
    homeAccessLevel: 'private' | 'friends_only' | 'public';
    gardenAccessLevel: 'private' | 'friends_only' | 'public';
}

export interface FullPrivateSpaceData extends PrivateSpaceSettings {
    homeItems: HomeItemState[];
    gardenPlots: GardenPlotState[];
}

export class SpaceService {
    private static instance: SpaceService;
    private itemService: ItemService;
    private userService: UserService;
    private friendService: FriendService;

    public static getInstance(): SpaceService {
        if (!SpaceService.instance) {
            SpaceService.instance = new SpaceService(ItemService.getInstance(), UserService.getInstance(), FriendService.getInstance());
        }
        return SpaceService.instance;
    }

    private constructor(
        itemService: ItemService,
        userService: UserService,
        friendService: FriendService
    ) {
        this.itemService = itemService;
        this.userService = userService;
        this.friendService = friendService;
    }

    /**
     * Retrieves or creates a user's private space settings.
     * @param ownerUserId The ID of the user.
     * @returns The user's private space settings.
     */
    public async getOrCreateUserSpaceSettings(ownerUserId: string): Promise<PrivateSpaceSettings> {
        const [rows] = await db.execute<RowDataPacket[]>(
            `SELECT * FROM user_private_spaces WHERE owner_user_id = ?;`,
            [ownerUserId]
        );
        if (rows.length > 0) {
            const row = rows[0];
            return {
                ownerUserId: String(row.owner_user_id),
                homeBackgroundId: row.home_background_id,
                gardenBackgroundId: row.garden_background_id,
                homeAccessLevel: row.home_access_level,
                gardenAccessLevel: row.garden_access_level
            };
        } else {
            // Create default settings if none exist
            await db.execute(
                `INSERT INTO user_private_spaces (owner_user_id) VALUES (?);`,
                [ownerUserId]
            );
            return {
                ownerUserId: ownerUserId,
                homeBackgroundId: "default_home_bg",
                gardenBackgroundId: "default_garden_bg",
                homeAccessLevel: "private",
                gardenAccessLevel: "private"
            };
        }
    }

    /**
     * Retrieves all data for a user's private space (settings, home items, garden plots).
     * @param ownerUserId The ID of the user.
     * @returns All private space data.
     */
    public async getFullPrivateSpaceData(ownerUserId: string): Promise<FullPrivateSpaceData> {
        const settings = await this.getOrCreateUserSpaceSettings(ownerUserId);

        const [homeItemsRows] = await db.execute<RowDataPacket[]>(
            `SELECT * FROM home_items WHERE owner_user_id = ?;`,
            [ownerUserId]
        );
        const homeItems = homeItemsRows.map(row => {
            const item = new HomeItemState();
            item.instanceId = row.item_instance_id;
            item.templateId = row.item_template_id;
            item.x = row.pos_x;
            item.y = row.pos_y;
            item.rotation = row.rotation;
            item.isFlipped = row.is_flipped === 1; // MySQL BOOLEAN (TINYINT(1))
            return item;
        });

        const [gardenPlotsRows] = await db.execute<RowDataPacket[]>(
            `SELECT * FROM garden_plots WHERE owner_user_id = ?;`,
            [ownerUserId]
        );
        const gardenPlots = gardenPlotsRows.map(row => {
            const plot = new GardenPlotState();
            plot.plotId = row.plot_id;
            plot.plotTemplateId = row.plot_template_id;
            plot.seedId = row.seed_id;
            plot.plantTimestamp = Number(row.plant_timestamp);
            plot.growthStage = row.growth_stage;
            plot.lastWateredTimestamp = Number(row.last_watered_timestamp);
            return plot;
        });

        return {
            ...settings,
            homeItems: homeItems,
            gardenPlots: gardenPlots,
        };
    }

    /**
     * Checks if a visitor has access to a specific private space.
     * @param ownerUserId The ID of the space owner.
     * @param visitorUserId The ID of the visiting user.
     * @param spaceType "home" or "garden".
     * @returns True if access is granted, false otherwise.
     */
    public async checkAccess(ownerUserId: string, visitorUserId: string, spaceType: 'home' | 'garden'): Promise<boolean> {
        if (ownerUserId === visitorUserId) {
            return true; // Owner always has access to their own space
        }

        const settings = await this.getOrCreateUserSpaceSettings(ownerUserId);
        const accessLevel = spaceType === 'home' ? settings.homeAccessLevel : settings.gardenAccessLevel;

        switch (accessLevel) {
            case 'public':
                return true;
            case 'friends_only':
                return await this.friendService.areFriends(ownerUserId, visitorUserId);
            case 'private':
            default:
                return false;
        }
    }

    /**
     * Places a new furniture item in the user's home.
     * @param ownerUserId User ID.
     * @param itemTemplateId The ID of the furniture item template.
     * @param x X coordinate.
     * @param y Y coordinate.
     * @param rotation Rotation.
     * @param isFlipped Whether the item is flipped.
     * @returns The newly created HomeItemState.
     */
    public async placeHomeItem(
        ownerUserId: string,
        itemTemplateId: string,
        x: number,
        y: number,
        rotation: number = 0,
        isFlipped: boolean = false
    ): Promise<HomeItemState> {
        const itemDetails = await this.itemService.getItemDetails(itemTemplateId);
        if (!itemDetails || itemDetails.item_type !== 'furniture') {
            throw new Error(`Item template ID ${itemTemplateId} is not a valid furniture item.`);
        }
        // TODO: Deduct item from user's inventory

        const itemInstanceId = uuidv4();

        await db.execute(
            `INSERT INTO home_items (item_instance_id, owner_user_id, item_template_id, pos_x, pos_y, rotation, is_flipped)
             VALUES (?, ?, ?, ?, ?, ?, ?);`,
            [itemInstanceId, ownerUserId, itemTemplateId, x, y, rotation, isFlipped]
        );

        const newHomeItem = new HomeItemState();
        newHomeItem.instanceId = itemInstanceId;
        newHomeItem.templateId = itemTemplateId;
        newHomeItem.x = x;
        newHomeItem.y = y;
        newHomeItem.rotation = rotation;
        newHomeItem.isFlipped = isFlipped;
        return newHomeItem;
    }

    /**
     * Updates the position or orientation of an existing home item.
     * @param ownerUserId User ID.
     * @param itemInstanceId The instance ID of the item to update.
     * @param updates Object containing fields to update (x, y, rotation, isFlipped).
     * @returns True if updated successfully, false otherwise.
     */
    public async updateHomeItemDetails(
        ownerUserId: string,
        itemInstanceId: string,
        updates: { x?: number; y?: number; rotation?: number; isFlipped?: boolean }
    ): Promise<boolean> {
        const setClauses: string[] = [];
        const params: (string | number | boolean)[] = [];

        if (typeof updates.x === 'number') { setClauses.push("pos_x = ?"); params.push(updates.x); }
        if (typeof updates.y === 'number') { setClauses.push("pos_y = ?"); params.push(updates.y); }
        if (typeof updates.rotation === 'number') { setClauses.push("rotation = ?"); params.push(updates.rotation); }
        if (typeof updates.isFlipped === 'boolean') { setClauses.push("is_flipped = ?"); params.push(updates.isFlipped); }

        if (setClauses.length === 0) {
            return false; // No updates provided
        }

        params.push(itemInstanceId, ownerUserId);

        const [result] = await db.execute(
            `UPDATE home_items SET ${setClauses.join(", ")}, updated_at = CURRENT_TIMESTAMP
             WHERE item_instance_id = ? AND owner_user_id = ?;`,
            params
        );
        return (result as any).affectedRows > 0;
    }

    /**
     * Removes a home item.
     * @param ownerUserId User ID.
     * @param itemInstanceId The instance ID of the item to remove.
     * @returns True if removed successfully, false otherwise.
     */
    public async removeHomeItem(ownerUserId: string, itemInstanceId: string): Promise<boolean> {
        const [result] = await db.execute(
            `DELETE FROM home_items WHERE item_instance_id = ? AND owner_user_id = ?;`,
            [itemInstanceId, ownerUserId]
        );
        return (result as any).affectedRows > 0;
    }

    /**
     * Plants a seed in a garden plot.
     * @param ownerUserId User ID.
     * @param plotId The ID of the plot.
     * @param seedItemId The ID of the seed item to plant.
     * @returns The updated GardenPlotState.
     */
    public async plantSeed(ownerUserId: string, plotId: string, seedItemId: string): Promise<GardenPlotState> {
        const seedDetails = await this.itemService.getItemDetails(seedItemId);
        if (!seedDetails || seedDetails.item_type !== 'seed') {
            throw new Error(`Item ID ${seedItemId} is not a valid seed item.`);
        }

        // TODO: Deduct seed from user's inventory

        const now = Date.now();
        const [result] = await db.execute(
            `UPDATE garden_plots SET seed_id = ?, plant_timestamp = ?, growth_stage = 0, last_watered_timestamp = ?
             WHERE owner_user_id = ? AND plot_id = ? AND seed_id IS NULL;`, // Only plant if plot is empty
            [seedItemId, now, now, ownerUserId, plotId]
        );

        if ((result as any).affectedRows === 0) {
            throw new Error(`Failed to plant seed. Plot ${plotId} might be occupied or not found.`);
        }

        const [updatedRows] = await db.execute<RowDataPacket[]>(
            `SELECT * FROM garden_plots WHERE owner_user_id = ? AND plot_id = ?;`,
            [ownerUserId, plotId]
        );
        const updatedPlot = new GardenPlotState();
        if (updatedRows.length > 0) {
            const row = updatedRows[0];
            updatedPlot.plotId = row.plot_id;
            updatedPlot.plotTemplateId = row.plot_template_id;
            updatedPlot.seedId = row.seed_id;
            updatedPlot.plantTimestamp = Number(row.plant_timestamp);
            updatedPlot.growthStage = row.growth_stage;
            updatedPlot.lastWateredTimestamp = Number(row.last_watered_timestamp);
        }
        return updatedPlot;
    }

    /**
     * Waters a plant in a garden plot.
     * @param ownerUserId User ID.
     * @param plotId The ID of the plot.
     * @returns The updated GardenPlotState.
     */
    public async waterPlant(ownerUserId: string, plotId: string): Promise<GardenPlotState> {
        const [plots] = await db.execute<RowDataPacket[]>(
            `SELECT seed_id, plant_timestamp FROM garden_plots WHERE owner_user_id = ? AND plot_id = ?;`,
            [ownerUserId, plotId]
        );

        if (plots.length === 0 || !plots[0].seed_id) {
            throw new Error(`Plot ${plotId} is empty or not found.`);
        }

        const seedDetails = await this.itemService.getItemDetails(plots[0].seed_id);
        if (!seedDetails || seedDetails.item_type !== 'seed') {
            throw new Error(`Invalid seed planted in plot ${plotId}.`);
        }

        const now = Date.now();
        // Check if watering is needed (e.g., based on seedDetails.water_interval_ms)
        // For simplicity, just update timestamp for now
        const [result] = await db.execute(
            `UPDATE garden_plots SET last_watered_timestamp = ?
             WHERE owner_user_id = ? AND plot_id = ?;`,
            [now, ownerUserId, plotId]
        );

        if ((result as any).affectedRows === 0) {
            throw new Error(`Failed to water plant in plot ${plotId}.`);
        }

        const [updatedRows] = await db.execute<RowDataPacket[]>(
            `SELECT * FROM garden_plots WHERE owner_user_id = ? AND plot_id = ?;`,
            [ownerUserId, plotId]
        );
        const updatedPlot = new GardenPlotState();
        if (updatedRows.length > 0) {
            const row = updatedRows[0];
            updatedPlot.plotId = row.plot_id;
            updatedPlot.plotTemplateId = row.plot_template_id;
            updatedPlot.seedId = row.seed_id;
            updatedPlot.plantTimestamp = Number(row.plant_timestamp);
            updatedPlot.growthStage = row.growth_stage;
            updatedPlot.lastWateredTimestamp = Number(row.last_watered_timestamp);
        }
        return updatedPlot;
    }

    /**
     * Updates the growth stage of a plant. (This would typically be called by a cron job or game tick).
     * @param ownerUserId User ID.
     * @param plotId The ID of the plot.
     * @param newGrowthStage The new growth stage.
     * @returns True if updated successfully, false otherwise.
     */
    public async updatePlantGrowthStage(ownerUserId: string, plotId: string, newGrowthStage: number): Promise<boolean> {
        const [result] = await db.execute(
            `UPDATE garden_plots SET growth_stage = ?, updated_at = CURRENT_TIMESTAMP
             WHERE owner_user_id = ? AND plot_id = ?;`,
            [newGrowthStage, ownerUserId, plotId]
        );
        return (result as any).affectedRows > 0;
    }

    /**
     * Harvests a plant from a garden plot.
     * @param ownerUserId User ID.
     * @param plotId The ID of the plot.
     * @returns Harvested items (e.g., [{itemId: "carrot", quantity: 1}]).
     */
    public async harvestPlant(ownerUserId: string, plotId: string): Promise<any[]> {
        const [plots] = await db.execute<RowDataPacket[]>(
            `SELECT seed_id, growth_stage FROM garden_plots WHERE owner_user_id = ? AND plot_id = ?;`,
            [ownerUserId, plotId]
        );

        if (plots.length === 0 || !plots[0].seed_id) {
            throw new Error(`Plot ${plotId} is empty or not found.`);
        }

        const seedDetails = await this.itemService.getItemDetails(plots[0].seed_id);
        if (!seedDetails || seedDetails.item_type !== 'seed') {
            throw new Error(`Invalid seed planted in plot ${plotId}.`);
        }

        const currentGrowthStage = plots[0].growth_stage;
        if (currentGrowthStage < seedDetails.num_growth_stages - 1) { // Assuming last stage is harvestable
            throw new Error(`Plant in plot ${plotId} is not ready for harvest.`);
        }

        const harvestedItems = JSON.parse(seedDetails.harvest_details || '[]');
        // TODO: Add items to user's inventory

        // Clear the plot
        await db.execute(
            `UPDATE garden_plots SET seed_id = NULL, plant_timestamp = 0, growth_stage = 0, last_watered_timestamp = 0
             WHERE owner_user_id = ? AND plot_id = ?;`,
            [ownerUserId, plotId]
        );

        return harvestedItems;
    }

    /**
     * Updates the background ID for a user's home or garden.
     * @param ownerUserId User ID.
     * @param spaceType "home" or "garden".
     * @param backgroundItemId The new background item ID.
     * @returns True if updated successfully, false otherwise.
     */
    public async updateSpaceBackground(ownerUserId: string, spaceType: 'home' | 'garden', backgroundItemId: string): Promise<boolean> {
        const itemDetails = await this.itemService.getItemDetails(backgroundItemId);
        if (!itemDetails || itemDetails.item_type !== 'background') {
            throw new Error(`Item ID ${backgroundItemId} is not a valid background item.`);
        }

        const field = spaceType === 'home' ? 'home_background_id' : 'garden_background_id';
        const [result] = await db.execute(
            `UPDATE user_private_spaces SET ${field} = ?, updated_at = CURRENT_TIMESTAMP
             WHERE owner_user_id = ?;`,
            [backgroundItemId, ownerUserId]
        );
        return (result as any).affectedRows > 0;
    }

    /**
     * Updates the access level for a user's home or garden.
     * @param ownerUserId User ID.
     * @param spaceType "home" or "garden".
     * @param accessLevel The new access level ('private', 'friends_only', 'public').
     * @returns True if updated successfully, false otherwise.
     */
    public async updateSpaceAccessLevel(ownerUserId: string, spaceType: 'home' | 'garden', accessLevel: 'private' | 'friends_only' | 'public'): Promise<boolean> {
        const field = spaceType === 'home' ? 'home_access_level' : 'garden_access_level';
        const [result] = await db.execute(
            `UPDATE user_private_spaces SET ${field} = ?, updated_at = CURRENT_TIMESTAMP
             WHERE owner_user_id = ?;`,
            [accessLevel, ownerUserId]
        );
        return (result as any).affectedRows > 0;
    }

    /**
     * Initializes default plots for a new user's garden.
     * @param ownerUserId User ID.
     * @param numPlots Number of default plots to create.
     */
    public async initializeDefaultGardenPlots(ownerUserId: string, numPlots: number = 5): Promise<void> {
        const existingPlots = await db.execute<RowDataPacket[]>(
            `SELECT COUNT(*) as count FROM garden_plots WHERE owner_user_id = ?;`,
            [ownerUserId]
        );
        if ((existingPlots[0] as any).count > 0) {
            console.log(`User ${ownerUserId} already has garden plots. Skipping default initialization.`);
            return;
        }

        const queries = [];
        for (let i = 0; i < numPlots; i++) {
            const plotId = `plot_${i + 1}`; // Simple incremental ID for plots
            queries.push(
                db.execute(
                    `INSERT INTO garden_plots (plot_id, owner_user_id, plot_template_id) VALUES (?, ?, ?);`,
                    [plotId, ownerUserId, 'basic_garden_plot'] // Assuming 'basic_garden_plot' as a default template
                )
            );
        }
        await Promise.all(queries);
        console.log(`Initialized ${numPlots} default garden plots for user ${ownerUserId}.`);
    }
}
```